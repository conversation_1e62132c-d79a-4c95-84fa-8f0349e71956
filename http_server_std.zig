const std = @import("std");

pub fn main() !void {
    // 解析服务器地址
    const address = try std.net.Address.parseIp4("127.0.0.1", 8080);

    // 创建服务器监听器
    var server = try address.listen(.{});
    defer server.deinit();

    std.debug.print("HTTP 服务器运行在 http://127.0.0.1:8080\n", .{});

    // 主循环处理连接
    while (true) {
        const connection = server.accept() catch |err| {
            std.debug.print("接受连接时出错: {}\n", .{err});
            continue;
        };

        handleConnection(connection) catch |err| {
            std.debug.print("处理连接时出错: {}\n", .{err});
        };
    }
}

fn handleConnection(conn: std.net.Server.Connection) !void {
    defer conn.stream.close();

    // 创建读取缓冲区
    var buffer: [1024]u8 = undefined;

    // 使用连接和缓冲区初始化 HTTP 服务器
    var http_server = std.http.Server.init(conn, &buffer);

    // 接收请求头
    var request = http_server.receiveHead() catch |err| {
        std.debug.print("接收请求头时出错: {}\n", .{err});
        return;
    };

    // 打印请求信息
    std.debug.print("收到请求: {s} {s}\n", .{ @tagName(request.head.method), request.head.target });

    // 根据路径处理不同的请求
    if (std.mem.eql(u8, request.head.target, "/")) {
        try request.respond("欢迎访问 Zig HTTP 服务器!\n", .{});
    } else if (std.mem.startsWith(u8, request.head.target, "/hello")) {
        try request.respond("Hello, World!\n", .{});
    } else if (std.mem.startsWith(u8, request.head.target, "/api/status")) {
        try request.respond("服务器状态: 正常运行\n", .{});
    } else {
        try request.respond("404 - 页面未找到\n", .{ .status = .not_found });
    }
}
